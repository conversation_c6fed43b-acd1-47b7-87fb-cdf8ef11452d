<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Headers para JSON y CORS
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Iniciar sesión
session_start();

// Log de debug
error_log("=== INTELETGROUP PROSPECTO DEBUG ===");
error_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
error_log("SESSION: " . print_r($_SESSION, true));
error_log("POST: " . print_r($_POST, true));

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Usuario no autenticado o sin permisos para InteletGroup'
    ]);
    exit;
}

// Verificar método POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Método no permitido'
    ]);
    exit;
}

// Incluir conexión a la base de datos
require_once 'con_db.php';

try {
    // Obtener datos del formulario
    $usuario_id = $_SESSION['usuario_id'];
    $nombre_ejecutivo = trim($_POST['nombre_ejecutivo'] ?? '');
    $rut_cliente = trim($_POST['rut_cliente'] ?? '');
    $razon_social = strtoupper(trim($_POST['razon_social'] ?? ''));
    $rubro = trim($_POST['rubro'] ?? '');
    $direccion_comercial = trim($_POST['direccion_comercial'] ?? '');
    $telefono_celular = trim($_POST['telefono_celular'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $numero_pos = trim($_POST['numero_pos'] ?? '');
    $tipo_cuenta = trim($_POST['tipo_cuenta'] ?? '');
    $numero_cuenta_bancaria = trim($_POST['numero_cuenta_bancaria'] ?? '');
    $dias_atencion = trim($_POST['dias_atencion'] ?? '');
    $horario_atencion = trim($_POST['horario_atencion'] ?? '');
    $contrata_boleta = trim($_POST['contrata_boleta'] ?? '');
    $competencia_actual = trim($_POST['competencia_actual'] ?? '');

    error_log("Datos recibidos - RUT: $rut_cliente, Razón Social: $razon_social");

    // Validaciones básicas
    $errores = [];

    if (empty($nombre_ejecutivo)) $errores[] = 'Nombre de ejecutivo es requerido';
    if (empty($rut_cliente)) $errores[] = 'RUT del cliente es requerido';
    if (empty($razon_social)) $errores[] = 'Razón social es requerida';
    if (empty($rubro)) $errores[] = 'Rubro es requerido';
    if (empty($direccion_comercial)) $errores[] = 'Dirección comercial es requerida';
    if (empty($telefono_celular)) $errores[] = 'Teléfono celular es requerido';
    if (empty($email)) $errores[] = 'Email es requerido';
    if (empty($tipo_cuenta)) $errores[] = 'Tipo de cuenta es requerido';
    if (empty($numero_cuenta_bancaria)) $errores[] = 'Número de cuenta bancaria es requerido';
    if (empty($dias_atencion)) $errores[] = 'Días de atención es requerido';
    if (empty($horario_atencion)) $errores[] = 'Horario de atención es requerido';
    if (empty($contrata_boleta)) $errores[] = 'Contrata boleta es requerido';
    if (empty($competencia_actual)) $errores[] = 'Competencia actual es requerida';

    // Validar formato RUT
    if (!preg_match('/^\d{7,8}-[\dkK]$/', $rut_cliente)) {
        $errores[] = 'Formato de RUT inválido';
    }

    // Validar formato teléfono
    if (!preg_match('/^\d{9,15}$/', $telefono_celular)) {
        $errores[] = 'Formato de teléfono inválido';
    }

    // Validar email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errores[] = 'Formato de email inválido';
    }

    // Validar razón social (solo letras mayúsculas y espacios)
    if (!preg_match('/^[A-Z\s]+$/', $razon_social)) {
        $errores[] = 'Razón social debe contener solo letras mayúsculas y espacios';
    }

    if (!empty($errores)) {
        echo json_encode([
            'success' => false,
            'message' => 'Errores de validación: ' . implode(', ', $errores)
        ]);
        exit;
    }

    // VERSIÓN TEMPORAL: Usar tabla de Experian existente
    // Verificar si el RUT ya existe en la tabla de Experian
    $stmt = $conexion->prepare("SELECT id FROM tb_experian_prospecto WHERE rut = ?");
    $stmt->bind_param("s", $rut_cliente);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Ya existe un prospecto con este RUT en el sistema'
        ]);
        exit;
    }

    // Manejar archivos subidos
    $documentos_adjuntos = [];
    if (isset($_FILES['documentos']) && !empty($_FILES['documentos']['name'][0])) {
        $upload_dir = 'uploads/inteletgroup_prospectos/';
        
        // Crear directorio si no existe
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $allowed_types = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        
        $max_size = 5 * 1024 * 1024; // 5MB
        
        for ($i = 0; $i < count($_FILES['documentos']['name']); $i++) {
            if ($_FILES['documentos']['error'][$i] === UPLOAD_ERR_OK) {
                $file_name = $_FILES['documentos']['name'][$i];
                $file_type = $_FILES['documentos']['type'][$i];
                $file_size = $_FILES['documentos']['size'][$i];
                $file_tmp = $_FILES['documentos']['tmp_name'][$i];
                
                // Validar tipo de archivo
                if (!in_array($file_type, $allowed_types)) {
                    echo json_encode([
                        'success' => false,
                        'message' => "Tipo de archivo no permitido: $file_name"
                    ]);
                    exit;
                }
                
                // Validar tamaño
                if ($file_size > $max_size) {
                    echo json_encode([
                        'success' => false,
                        'message' => "Archivo muy grande: $file_name"
                    ]);
                    exit;
                }
                
                // Generar nombre único
                $extension = pathinfo($file_name, PATHINFO_EXTENSION);
                $unique_name = $rut_cliente . '_' . time() . '_' . $i . '.' . $extension;
                $file_path = $upload_dir . $unique_name;
                
                // Mover archivo
                if (move_uploaded_file($file_tmp, $file_path)) {
                    $documentos_adjuntos[] = [
                        'nombre_original' => $file_name,
                        'nombre_archivo' => $unique_name,
                        'tipo_archivo' => $file_type,
                        'tamaño_archivo' => $file_size,
                        'ruta_archivo' => $file_path
                    ];
                }
            }
        }
    }

    // Iniciar transacción
    $conexion->begin_transaction();

    try {
        // VERSIÓN TEMPORAL: Insertar en tabla de Experian existente con campos adicionales
        $observaciones = "INTELETGROUP - Tipo Cuenta: $tipo_cuenta, N° Cuenta: $numero_cuenta_bancaria, POS: $numero_pos, Días: $dias_atencion, Horario: $horario_atencion, Boleta: $contrata_boleta, Competencia: $competencia_actual";

        $sql = "INSERT INTO tb_experian_prospecto (
            usuario_id, nombre_ejecutivo, rut, razon_social, rubro,
            direccion, telefono, email, observaciones
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conexion->prepare($sql);
        $stmt->bind_param("issssssss",
            $usuario_id, $nombre_ejecutivo, $rut_cliente, $razon_social, $rubro,
            $direccion_comercial, $telefono_celular, $email, $observaciones
        );

        if (!$stmt->execute()) {
            throw new Exception('Error al insertar prospecto: ' . $stmt->error);
        }

        $prospecto_id = $conexion->insert_id;

        // NOTA: Documentos se manejarán en una versión futura
        error_log("Prospecto InteletGroup creado exitosamente - ID: $prospecto_id");

        // Insertar en bitácora de Experian existente
        $sql_bitacora = "INSERT INTO tb_experian_prospecto_bitacora (
            rut, observaciones, estado, usuario_id
        ) VALUES (?, ?, 'Envio información', ?)";

        $descripcion_bitacora = "INTELETGROUP - Prospecto creado: $razon_social";

        $stmt_bitacora = $conexion->prepare($sql_bitacora);
        $stmt_bitacora->bind_param("ssi", $rut_cliente, $descripcion_bitacora, $usuario_id);
        $stmt_bitacora->execute();

        // Confirmar transacción
        $conexion->commit();

        // Enviar email de notificación
        enviarEmailNotificacion($rut_cliente, $razon_social, $nombre_ejecutivo, $_POST);

        echo json_encode([
            'success' => true,
            'message' => 'Prospecto registrado exitosamente',
            'prospecto_id' => $prospecto_id
        ]);

    } catch (Exception $e) {
        $conexion->rollback();
        throw $e;
    }

} catch (Exception $e) {
    error_log("Error en guardar_inteletgroup_prospecto.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error interno del servidor: ' . $e->getMessage()
    ]);
}

// Función para enviar email de notificación
function enviarEmailNotificacion($rut, $razon_social, $ejecutivo, $datos) {
    $to = '<EMAIL>'; // Cambiar por email real
    $subject = "Nuevo Prospecto Registrado - RUT: $rut";
    
    $body = "
    <h2>Nuevo Prospecto Registrado</h2>
    <p><strong>Ejecutivo:</strong> $ejecutivo</p>
    <p><strong>RUT Cliente:</strong> $rut</p>
    <p><strong>Razón Social:</strong> $razon_social</p>
    <p><strong>Rubro:</strong> {$datos['rubro']}</p>
    <p><strong>Dirección:</strong> {$datos['direccion_comercial']}</p>
    <p><strong>Teléfono:</strong> {$datos['telefono_celular']}</p>
    <p><strong>Email:</strong> {$datos['email']}</p>
    <p><strong>Tipo de Cuenta:</strong> {$datos['tipo_cuenta']}</p>
    <p><strong>Competencia Actual:</strong> {$datos['competencia_actual']}</p>
    <p><strong>Fecha de Registro:</strong> " . date('Y-m-d H:i:s') . "</p>
    ";
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: <EMAIL>" . "\r\n";
    
    // Enviar email (comentado para desarrollo)
    // mail($to, $subject, $body, $headers);
}
?>
