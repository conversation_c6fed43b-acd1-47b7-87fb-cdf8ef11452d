<?php
// Configurar reporte de errores
error_reporting(E_ALL);
// Solo mostrar errores si este archivo se ejecuta directamente
ini_set('display_errors', basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__) ? 1 : 0);

// Crear carpeta de logs si no existe
$log_dir = dirname(__FILE__) . '/logs';
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}

// Función para log más detallado
function escribir_log($mensaje, $tipo = 'error') {
    $log_dir = dirname(__FILE__) . '/logs';
    $log_file = $log_dir . '/db_errors.log';
    
    // Asegurarse de que el directorio de logs existe
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $fecha = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $url = $_SERVER['REQUEST_URI'] ?? 'Unknown';
    $mensaje_log = "[$fecha] [$tipo] [$ip] [$url] $mensaje" . PHP_EOL;
    
    // Escribir en el log
    error_log($mensaje_log, 3, $log_file);
    
    // Para errores, también escribir en el log de errores de PHP
    if ($tipo == 'error') {
        error_log("DB ERROR: $mensaje");
    }
}

// Solo escribir logs si no estamos en un contexto que requiere JSON limpio
if (basename($_SERVER['SCRIPT_FILENAME']) != 'ControllerGestar.php') {
    escribir_log("Intentando conexión a la base de datos", 'info');
}

// Configuración de la conexión a la base de datos
// IMPORTANTE: Definir variables para entorno de producción y desarrollo
if ($_SERVER['HTTP_HOST'] == 'www.gestarservicios.cl' || $_SERVER['HTTP_HOST'] == 'gestarservicios.cl') {
    // Entorno de producción
    $host = 'localhost';          // Usar localhost en lugar de una IP
    $port = 3306;                 // Puerto estándar de MySQL
    $dbname = 'gestarse_experian';
    $username = 'gestarse_ncornejo7_experian';
    $password = 'N1c0l7as17';
    error_log("Usando configuración de producción para base de datos");
} else {
    // Entorno de desarrollo/local - estos valores deben ser configurados según tu entorno local
    $host = 'localhost';         // Ajusta según sea necesario
    $port = 3306;                // Ajusta según sea necesario
    $dbname = 'gestarse_experian';
    $username = 'root';          // Ajusta según sea necesario
    $password = '';              // Ajusta según sea necesario
    error_log("Usando configuración de desarrollo para base de datos");
}

// Debug de entorno - solo si no estamos en contexto JSON
$is_json_context = basename($_SERVER['SCRIPT_FILENAME']) == 'ControllerGestar.php';
if (!$is_json_context) {
    escribir_log("Ambiente de ejecución: " . php_uname(), 'info');
}

try {
    if (!$is_json_context) {
        escribir_log("Parámetros de conexión: host=$host, dbname=$dbname, user=$username, port=$port", 'info');
    }

    // Intentar conexión con mysqli
    if (!$is_json_context) {
        escribir_log("Intentando conexión a $host:$port...", 'info');
    }
    $mysqli = @new mysqli($host, $username, $password, $dbname, $port);
    $conn = $mysqli; // Asignar la conexión a $conn para mantener compatibilidad
    if (!$is_json_context) {
        escribir_log("Conexión mysqli creada", 'info');
    }

    if ($mysqli->connect_error) {
        escribir_log("Error de conexión (mysqli): " . $mysqli->connect_error . " (Código: " . $mysqli->connect_errno . ")");

        // Solo mostrar error si este archivo se ejecuta directamente
        if (basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__)) {
            die("Error de conexión (detalles): " . $mysqli->connect_error . " (Código: " . $mysqli->connect_errno . ")");
        } else {
            throw new Exception("Error de conexión a la base de datos: " . $mysqli->connect_error);
        }
    }

    if (!$is_json_context) {
        escribir_log("Conexión establecida, verificando con consulta simple", 'info');
    }

    // Verificar la conexión con una consulta simple
    if (!$mysqli->query("SELECT 1")) {
        escribir_log("Error en la consulta de verificación: " . $mysqli->error);

        // Solo mostrar error si este archivo se ejecuta directamente
        if (basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__)) {
            die("Error en la consulta de verificación: " . $mysqli->error);
        } else {
            throw new Exception("Error en la consulta de verificación: " . $mysqli->error);
        }
    }

    if (!$is_json_context) {
        escribir_log("Conexión verificada correctamente", 'info');
    }

} catch (Exception $e) {
    escribir_log("Excepción en conexión: " . $e->getMessage());

    // Si este archivo se incluye desde un archivo que espera JSON, mostrar error en formato JSON
    if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
        exit;
    } else if (basename($_SERVER['SCRIPT_FILENAME']) != basename(__FILE__)) {
        // Si se incluye desde otro archivo pero no es JSON, re-lanzar la excepción
        throw $e;
    } else {
        // Si este archivo se ejecuta directamente, mostrar el error normalmente
        die($e->getMessage());
    }
}

// Función para obtener una conexión PDO
// IMPORTANTE: Renombrada para evitar conflicto con la función en form_experian2.php
function createPDOConnection() {
    global $mysqli, $conn, $host, $dbname, $username, $password;

    // Si ya existe una conexión mysqli, crear y devolver una conexión PDO equivalente
    if (isset($mysqli) && $mysqli instanceof mysqli && !$mysqli->connect_error) {
        try {
            return new PDO(
                "mysql:host=$host;dbname=$dbname;charset=utf8",
                $username,
                $password,
                array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
            );
        } catch (PDOException $e) {
            error_log("Error creando conexión PDO desde mysqli existente: " . $e->getMessage());
            return null;
        }
    }

    // Si no hay conexión existente, crear una nueva
    try {
        $pdo = new PDO(
            "mysql:host=$host;dbname=$dbname;charset=utf8",
            $username,
            $password,
            array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
        );
        return $pdo;
    } catch (PDOException $e) {
        error_log("Error creando nueva conexión a la base de datos: " . $e->getMessage());
        return null;
    }
}

// Alias de compatibilidad para scripts existentes que usen getDBConnection()
if (!function_exists('getDBConnection')) {
    function getDBConnection() {
        return createPDOConnection();
    }
}

// Función para cerrar la conexión
function cerrarConexion() {
    global $mysqli, $conn;
    if (isset($mysqli)) {
        $mysqli->close();
        // Solo escribir log si no estamos en contexto JSON
        if (basename($_SERVER['SCRIPT_FILENAME']) != 'ControllerGestar.php') {
            escribir_log("Conexión cerrada", 'info');
        }
    }
}

// Registrar la función para cerrar la conexión al finalizar el script
register_shutdown_function('cerrarConexion');

// Solo mostrar el mensaje si este archivo se ejecuta directamente y no como inclusión
if (basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__)) {
    echo "Conexión a la base de datos establecida correctamente";
}