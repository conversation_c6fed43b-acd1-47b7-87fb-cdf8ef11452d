// JavaScript para el formulario de prospectos InteletGroup

// Variables globales
let currentUserName = '';
let currentUserId = 0;

// Inicialización cuando el DOM está listo
document.addEventListener('DOMContentLoaded', function() {
    initializeInteletGroupProspectForm();
});

// Función principal de inicialización
function initializeInteletGroupProspectForm() {
    // Obtener información del usuario actual
    if (typeof window.currentUserName !== 'undefined') {
        currentUserName = window.currentUserName;
    }
    if (typeof window.currentUserId !== 'undefined') {
        currentUserId = window.currentUserId;
    }

    // Configurar eventos
    setupFormEvents();
    setupValidation();
    setupFileUpload();
}

// Configurar eventos del formulario
function setupFormEvents() {
    // Botón de guardar
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    if (saveBtn) {
        saveBtn.addEventListener('click', handleSaveProspect);
    }

    // Botón de llenar datos de prueba
    const fillTestBtn = document.getElementById('fillTestDataBtn');
    if (fillTestBtn) {
        fillTestBtn.addEventListener('click', fillTestData);
    }

    // Evento cuando se abre el modal
    const modal = document.getElementById('inteletGroupProspectModal');
    if (modal) {
        modal.addEventListener('show.bs.modal', function() {
            resetForm();
            populateExecutiveName();
        });
    }
}

// Configurar validación en tiempo real
function setupValidation() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return;

    // RUT validation
    const rutField = document.getElementById('rut_cliente');
    if (rutField) {
        rutField.addEventListener('input', function() {
            validateRUT(this);
        });
    }

    // Razón Social validation
    const razonSocialField = document.getElementById('razon_social');
    if (razonSocialField) {
        razonSocialField.addEventListener('input', function() {
            validateRazonSocial(this);
        });
    }

    // Teléfono validation
    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField) {
        telefonoField.addEventListener('input', function() {
            validateTelefono(this);
        });
    }

    // Email validation
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('input', function() {
            validateEmail(this);
        });
    }

    // Validación general para campos requeridos
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateRequired(this);
        });
    });
}

// Configurar subida de archivos
function setupFileUpload() {
    const fileInput = document.getElementById('documentos');
    if (!fileInput) return;

    fileInput.addEventListener('change', function() {
        validateFiles(this);
    });
}

// Validar RUT
function validateRUT(field) {
    const value = field.value.trim();
    const rutPattern = /^\d{7,8}-[\dkK]$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!rutPattern.test(value)) {
        setFieldState(field, 'invalid', 'Formato inválido. Use: ********-9');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar Razón Social
function validateRazonSocial(field) {
    const value = field.value.trim();
    const pattern = /^[A-Z\s]+$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Solo letras mayúsculas y espacios');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar teléfono
function validateTelefono(field) {
    const value = field.value.trim();
    const pattern = /^\d{9,15}$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Solo números, mínimo 9 dígitos');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar email
function validateEmail(field) {
    const value = field.value.trim();
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Formato de email inválido');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar campos requeridos
function validateRequired(field) {
    const value = field.value.trim();
    
    if (value === '') {
        setFieldState(field, 'invalid', 'Este campo es requerido');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar archivos
function validateFiles(fileInput) {
    const files = fileInput.files;
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    let isValid = true;
    let errorMessage = '';
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        if (file.size > maxSize) {
            isValid = false;
            errorMessage = `El archivo ${file.name} excede el tamaño máximo de 5MB`;
            break;
        }
        
        if (!allowedTypes.includes(file.type)) {
            isValid = false;
            errorMessage = `El archivo ${file.name} no tiene un formato permitido`;
            break;
        }
    }
    
    if (!isValid) {
        setFieldState(fileInput, 'invalid', errorMessage);
        fileInput.value = '';
    } else {
        setFieldState(fileInput, 'valid');
    }
    
    return isValid;
}

// Establecer estado del campo
function setFieldState(field, state, message = '') {
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    
    // Limpiar clases anteriores
    field.classList.remove('is-valid', 'is-invalid');
    
    switch (state) {
        case 'valid':
            field.classList.add('is-valid');
            if (feedback) feedback.textContent = '';
            break;
        case 'invalid':
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = message;
            break;
        case 'neutral':
            if (feedback) feedback.textContent = '';
            break;
    }
}

// Poblar nombre del ejecutivo
function populateExecutiveName() {
    const nameField = document.getElementById('nombre_ejecutivo');
    if (nameField && currentUserName) {
        nameField.value = currentUserName;
    }
}

// Llenar datos de prueba
function fillTestData() {
    const testData = {
        rut_cliente: '********-9',
        razon_social: 'EMPRESA EJEMPLO LTDA',
        rubro: 'COMERCIO AL POR MENOR',
        direccion_comercial: 'AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO',
        telefono_celular: '*********',
        email: '<EMAIL>',
        numero_pos: 'POS123456',
        tipo_cuenta: 'Cuenta Corriente',
        numero_cuenta_bancaria: '********',
        dias_atencion: 'LUNES A VIERNES',
        horario_atencion: '09:00 - 18:00',
        contrata_boleta: 'Si',
        competencia_actual: 'Transbank'
    };
    
    Object.keys(testData).forEach(key => {
        const field = document.getElementById(key);
        if (field) {
            field.value = testData[key];
            // Trigger validation
            field.dispatchEvent(new Event('input'));
        }
    });
    
    showMessage('info', 'Datos de prueba cargados correctamente');
}

// Validar todo el formulario
function validateForm() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return false;
    
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!validateRequired(field)) {
            isValid = false;
        }
    });
    
    // Validaciones específicas
    const rutField = document.getElementById('rut_cliente');
    if (rutField && !validateRUT(rutField)) {
        isValid = false;
    }
    
    const razonSocialField = document.getElementById('razon_social');
    if (razonSocialField && !validateRazonSocial(razonSocialField)) {
        isValid = false;
    }
    
    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField && !validateTelefono(telefonoField)) {
        isValid = false;
    }
    
    const emailField = document.getElementById('email');
    if (emailField && !validateEmail(emailField)) {
        isValid = false;
    }
    
    return isValid;
}

// Manejar guardado del prospecto usando método directo sin service worker
function handleSaveProspect() {
    if (!validateForm()) {
        showMessage('error', 'Por favor, corrija los errores en el formulario antes de continuar');
        return;
    }

    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    const form = document.getElementById('inteletGroupProspectForm');

    // Mostrar estado de carga
    saveBtn.classList.add('loading');
    saveBtn.disabled = true;
    showMessage('info', 'Guardando prospecto InteletGroup...');

    // Crear un timestamp único para evitar cache
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);

    // Crear formulario con método POST directo
    const hiddenForm = document.createElement('form');
    hiddenForm.method = 'POST';
    hiddenForm.action = `guardar_inteletgroup_prospecto.php?t=${timestamp}&r=${randomId}`;
    hiddenForm.style.display = 'none';
    hiddenForm.enctype = 'application/x-www-form-urlencoded';

    // Obtener todos los datos del formulario
    const formData = new FormData(form);
    formData.append('usuario_id', currentUserId);
    formData.append('timestamp', timestamp);
    formData.append('bypass_sw', '1'); // Flag para bypass service worker

    // Convertir FormData a campos ocultos
    for (let [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        hiddenForm.appendChild(input);
    }

    // Crear iframe para capturar respuesta
    const responseIframe = document.createElement('iframe');
    responseIframe.name = `response_${timestamp}`;
    responseIframe.style.display = 'none';
    responseIframe.style.width = '0';
    responseIframe.style.height = '0';
    responseIframe.style.border = 'none';

    // Configurar target del formulario
    hiddenForm.target = responseIframe.name;

    // Agregar elementos al DOM
    document.body.appendChild(responseIframe);
    document.body.appendChild(hiddenForm);

    // Configurar listener para respuesta
    responseIframe.onload = function() {
        try {
            // Esperar un momento para que el contenido se cargue
            setTimeout(() => {
                try {
                    const iframeDoc = responseIframe.contentDocument || responseIframe.contentWindow.document;
                    const responseText = iframeDoc.body.textContent || iframeDoc.body.innerText;

                    console.log('Response from server:', responseText);

                    // Verificar si hay respuesta
                    if (responseText && responseText.trim()) {
                        try {
                            const result = JSON.parse(responseText);
                            
                            if (result.success) {
                                showMessage('success', result.message || 'Prospecto InteletGroup guardado exitosamente');
                                setTimeout(() => {
                                    const modal = bootstrap.Modal.getInstance(document.getElementById('inteletGroupProspectModal'));
                                    modal.hide();
                                    // Recargar la página para mostrar el nuevo prospecto
                                    location.reload();
                                }, 2000);
                            } else {
                                showMessage('error', result.message || 'Error al guardar el prospecto');
                                console.error('Error del servidor:', result);
                            }
                        } catch (jsonError) {
                            console.error('Error al parsear JSON:', jsonError);
                            // Mostrar el texto de respuesta en lugar de error genérico
                            if (responseText.includes("Fatal error") || responseText.includes("Parse error")) {
                                showMessage('error', 'Error en el servidor: ' + responseText.substring(0, 100) + '...');
                            } else {
                                showMessage('error', 'Respuesta no válida del servidor');
                            }
                        }
                    } else {
                        console.error('Respuesta vacía del servidor');
                        showMessage('error', 'No se recibió respuesta del servidor. Verifique la conexión a la base de datos.');
                    }
                } catch (parseError) {
                    console.error('Error parsing response:', parseError);
                    showMessage('error', 'Error al procesar la respuesta del servidor');
                }

                // Limpiar elementos
                document.body.removeChild(hiddenForm);
                document.body.removeChild(responseIframe);

                // Restaurar botón
                saveBtn.classList.remove('loading');
                saveBtn.disabled = false;
            }, 1000);

        } catch (error) {
            console.error('Error accessing iframe:', error);
            showMessage('error', 'Error de comunicación con el servidor');

            // Limpiar elementos
            document.body.removeChild(hiddenForm);
            document.body.removeChild(responseIframe);

            // Restaurar botón
            saveBtn.classList.remove('loading');
            saveBtn.disabled = false;
        }
    };

    // Manejar errores de carga
    responseIframe.onerror = function() {
        showMessage('error', 'Error de conexión con el servidor');

        // Limpiar elementos
        document.body.removeChild(hiddenForm);
        document.body.removeChild(responseIframe);

        // Restaurar botón
        saveBtn.classList.remove('loading');
        saveBtn.disabled = false;
    };

    // Enviar formulario
    console.log('Submitting form to:', hiddenForm.action);
    hiddenForm.submit();
}

// Mostrar mensajes
function showMessage(type, message) {
    const container = document.getElementById('inteletgroup-message-container');
    const successMsg = document.getElementById('inteletgroup-success-message');
    const errorMsg = document.getElementById('inteletgroup-error-message');
    const loadingMsg = document.getElementById('inteletgroup-loading-message');
    
    // Ocultar todos los mensajes
    successMsg.style.display = 'none';
    errorMsg.style.display = 'none';
    loadingMsg.style.display = 'none';
    
    // Mostrar el mensaje apropiado
    let targetMsg;
    switch (type) {
        case 'success':
            targetMsg = successMsg;
            break;
        case 'error':
            targetMsg = errorMsg;
            break;
        case 'info':
            targetMsg = loadingMsg;
            break;
    }
    
    if (targetMsg) {
        targetMsg.querySelector('.message-text').textContent = message;
        targetMsg.style.display = 'block';
        container.style.display = 'block';
        
        // Auto-hide después de 5 segundos para mensajes de éxito
        if (type === 'success') {
            setTimeout(() => {
                container.style.display = 'none';
            }, 5000);
        }
    }
}

// Resetear formulario
function resetForm() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (form) {
        form.reset();
        
        // Limpiar estados de validación
        const fields = form.querySelectorAll('.form-control, .form-select');
        fields.forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });
        
        // Limpiar mensajes
        const feedbacks = form.querySelectorAll('.invalid-feedback');
        feedbacks.forEach(feedback => {
            feedback.textContent = '';
        });
    }
    
    // Ocultar mensajes
    const container = document.getElementById('inteletgroup-message-container');
    if (container) {
        container.style.display = 'none';
    }
}

// Función para abrir el modal (llamada desde el botón)
function abrirModalInteletGroupProspecto() {
    const modal = new bootstrap.Modal(document.getElementById('inteletGroupProspectModal'));
    modal.show();
}
