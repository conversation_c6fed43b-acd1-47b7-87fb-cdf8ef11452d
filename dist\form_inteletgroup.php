<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'cache_utils.php';
session_start();

// Inicializar variables importantes
$es_admin = false; // Variable que indica si el usuario es administrador

// Aplicar headers anti-caché
no_cache_headers();

// Verificación mejorada de autenticación
function checkAuth() {
    if (!isset($_SESSION['usuario']) || empty($_SESSION['usuario'])) {
        // Registrar el intento fallido
        error_log("[" . date('Y-m-d H:i:s') . "] Error de autenticación - Usuario no identificado - IP: " . $_SERVER['REMOTE_ADDR']);

        // Redireccionar a login con mensaje de error
        $errorMsg = urlencode("Usuario no autenticado. Inicie sesión para continuar.");
        header('Location: ' . version_url('login.php?error=' . $errorMsg));
        exit;
    }

    // Renovar la sesión para evitar su caducidad prematura
    $_SESSION['last_activity'] = time();
    return true;
}

// Verificar si el usuario está autenticado
checkAuth();

// Verificar que el usuario pertenezca al proyecto InteletGroup
if (!isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    error_log("[" . date('Y-m-d H:i:s') . "] Acceso denegado - Usuario no pertenece al proyecto InteletGroup - Usuario: " . $_SESSION['usuario']);
    $errorMsg = urlencode("Acceso denegado. No tiene permisos para acceder a esta sección.");
    header('Location: ' . version_url('login.php?error=' . $errorMsg));
    exit;
}

// Incluir el archivo de conexión
require_once 'con_db.php';

// Obtener información del usuario logueado
$usuario_id = $_SESSION['usuario_id'];
$nombre_usuario = $_SESSION['nombre_usuario'] ?? 'Usuario';
$proyecto = $_SESSION['proyecto'] ?? 'inteletGroup';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InteletGroup - Panel de Control</title>
    <?php echo no_cache_meta(); ?>
    <link rel="stylesheet" href="<?php echo version_url('css/form_experian.css'); ?>">
    <!-- Estilos para tablas ordenables -->
    <link rel="stylesheet" href="<?php echo version_url('css/table-sort.css'); ?>">
    <!-- Estilos para formulario de prospectos InteletGroup -->
    <link rel="stylesheet" href="<?php echo version_url('css/inteletgroup-prospect.css'); ?>">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    
    <style>
        .inteletgroup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
        }
        
        .project-badge {
            background-color: #28a745;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            border-radius: 15px;
        }
        
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        
        .coming-soon {
            opacity: 0.7;
            position: relative;
        }
        
        .coming-soon::after {
            content: "Próximamente";
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ffc107;
            color: #000;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="inteletgroup-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <img src="img/logo-inteletgroup.png" alt="Logo InteletGroup" height="40" class="me-3" 
                             onerror="this.style.display='none'">
                        <div>
                            <h4 class="mb-0">InteletGroup</h4>
                            <small>Panel de Control</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <span class="project-badge me-3"><?php echo htmlspecialchars($proyecto); ?></span>
                        <div class="me-3">
                            <small>Bienvenido,</small><br>
                            <strong><?php echo htmlspecialchars($nombre_usuario); ?></strong>
                        </div>
                        <a href="logout.php" class="btn btn-outline-light btn-sm">
                            <i class="bi bi-box-arrow-right"></i> Salir
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Welcome Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card welcome-card">
                    <div class="card-body text-center py-5">
                        <h2 class="card-title mb-3">
                            <i class="bi bi-rocket-takeoff me-2"></i>
                            Bienvenido a InteletGroup
                        </h2>
                        <p class="card-text lead">
                            Tu plataforma integral para la gestión de proyectos y clientes
                        </p>
                        <p class="card-text">
                            Accede a todas las herramientas que necesitas para impulsar tu negocio
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Grid -->
        <div class="row g-4">
            <!-- Registro de Prospectos -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100" style="border: 2px solid #28a745;">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-person-plus-fill text-success" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title text-success">Registro de Prospectos</h5>
                        <p class="card-text">
                            Registra nuevos prospectos comerciales con toda su información y documentación.
                        </p>
                        <button class="btn btn-success" onclick="abrirModalInteletGroupProspecto()">
                            <i class="bi bi-plus-circle me-1"></i> Nuevo Prospecto
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gestión de Documentos -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100" style="border: 2px solid #17a2b8;">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-file-earmark-arrow-up text-info" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title text-info">Gestión de Documentos</h5>
                        <p class="card-text">
                            Sube y gestiona documentos de prospectos existentes por RUT.
                        </p>
                        <a href="inteletgroup_documentos.php" class="btn btn-info">
                            <i class="bi bi-files me-1"></i> Gestionar Docs
                        </a>
                    </div>
                </div>
            </div>

            <!-- Reportes -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100 coming-soon">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-graph-up text-info" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">Reportes y Analytics</h5>
                        <p class="card-text">
                            Obtén insights valiosos con reportes detallados y análisis de datos.
                        </p>
                        <button class="btn btn-info" disabled>
                            <i class="bi bi-arrow-right me-1"></i> Acceder
                        </button>
                    </div>
                </div>
            </div>

            <!-- Configuración -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100 coming-soon">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-gear-fill text-warning" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">Configuración</h5>
                        <p class="card-text">
                            Personaliza tu experiencia y configura las preferencias del sistema.
                        </p>
                        <button class="btn btn-warning" disabled>
                            <i class="bi bi-arrow-right me-1"></i> Acceder
                        </button>
                    </div>
                </div>
            </div>

            <!-- Soporte -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-headset text-secondary" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">Soporte Técnico</h5>
                        <p class="card-text">
                            ¿Necesitas ayuda? Contacta con nuestro equipo de soporte técnico.
                        </p>
                        <a href="mailto:<EMAIL>" class="btn btn-secondary">
                            <i class="bi bi-envelope me-1"></i> Contactar
                        </a>
                    </div>
                </div>
            </div>

            <!-- Acceso Temporal a Experian -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100" style="border: 2px solid #dc3545;">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-link-45deg text-danger" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title text-danger">Acceso Temporal</h5>
                        <p class="card-text">
                            Acceso temporal al sistema Experian mientras desarrollamos las nuevas funcionalidades.
                        </p>
                        <a href="form_experian2.php" class="btn btn-danger">
                            <i class="bi bi-arrow-right me-1"></i> Ir a Experian
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Information Alert -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="alert alert-info" role="alert">
                    <h4 class="alert-heading">
                        <i class="bi bi-info-circle me-2"></i>
                        Información Importante
                    </h4>
                    <p>
                        Bienvenido al nuevo panel de InteletGroup. Estamos trabajando en el desarrollo de nuevas 
                        funcionalidades específicas para tu proyecto. Mientras tanto, puedes acceder temporalmente 
                        al sistema Experian usando el enlace de "Acceso Temporal" arriba.
                    </p>
                    <hr>
                    <p class="mb-0">
                        <strong>Fecha de lanzamiento estimada:</strong> Próximamente<br>
                        <strong>Contacto para consultas:</strong> <EMAIL>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4 bg-light">
        <div class="container text-center">
            <p class="mb-0 text-muted">
                &copy; <?php echo date('Y'); ?> InteletGroup. Todos los derechos reservados.
            </p>
        </div>
    </footer>

    <!-- Modal del formulario de prospectos -->
    <?php include 'inteletgroup_prospect_modal.html'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript del formulario de prospectos -->
    <script src="<?php echo version_url('js/inteletgroup-prospect.js'); ?>"></script>

    <script>
        // Variables globales para el formulario
        window.currentUserName = '<?php echo addslashes($nombre_usuario); ?>';
        window.currentUserId = <?php echo $usuario_id; ?>;

        // Log del proyecto para debugging
        console.log('InteletGroup Panel - Usuario:', '<?php echo htmlspecialchars($nombre_usuario); ?>');
        console.log('Proyecto:', '<?php echo htmlspecialchars($proyecto); ?>');
        console.log('Usuario ID:', <?php echo $usuario_id; ?>);

        // Mensaje de bienvenida
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Panel de InteletGroup cargado correctamente');
        });
    </script>
</body>
</html>
