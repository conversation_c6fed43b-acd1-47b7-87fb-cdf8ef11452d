<?php
// Intentar diferentes rutas para con_db.php
$possible_paths = [
    "con_db.php",           // Mismo directorio
    "../con_db.php",        // Directorio padre
    "dist/con_db.php",      // Subdirectorio dist
    "../../con_db.php"      // Dos niveles arriba
];

$db_connected = false;
foreach ($possible_paths as $path) {
    if (file_exists($path)) {
        try {
            require_once($path);
            $db_connected = true;
            echo "<p style='color: green;'>✅ Conexión encontrada en: $path</p>";
            break;
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Error en $path: " . $e->getMessage() . "</p>";
        }
    }
}

if (!$db_connected) {
    echo "<p style='color: red;'>❌ No se pudo encontrar con_db.php en ninguna ubicación</p>";
    echo "<p>Rutas intentadas:</p><ul>";
    foreach ($possible_paths as $path) {
        echo "<li>" . realpath('.') . "/" . $path . "</li>";
    }
    echo "</ul>";
    exit();
}

// Verificar que la conexión MySQL existe
if (!isset($mysqli)) {
    echo "<p style='color: red;'>❌ Variable \$mysqli no está definida</p>";
    exit();
}

if ($mysqli->connect_error) {
    echo "<p style='color: red;'>❌ Error de conexión MySQL: " . $mysqli->connect_error . "</p>";
    exit();
}

try {
    echo "<h2>Actualizando esquema de tb_experian_usuarios</h2>\n";
    echo "<p style='color: blue;'>📍 Directorio actual: " . realpath('.') . "</p>";
    echo "<p style='color: blue;'>🔗 Conexión MySQL: OK</p>";
    
    // Verificar si la columna proyecto ya existe
    $check_proyecto = $mysqli->query("SHOW COLUMNS FROM tb_experian_usuarios LIKE 'proyecto'");
    
    if ($check_proyecto->num_rows === 0) {
        // La columna proyecto no existe, agregarla
        $sql_proyecto = "ALTER TABLE tb_experian_usuarios ADD COLUMN proyecto VARCHAR(100) DEFAULT 'inteletGroup' AFTER rol";
        
        if (!$mysqli->query($sql_proyecto)) {
            throw new Exception("Error al agregar la columna proyecto: " . $mysqli->error);
        }
        echo "✓ Columna 'proyecto' agregada correctamente<br>\n";
    } else {
        echo "✓ La columna 'proyecto' ya existe<br>\n";
    }
    
    // Verificar si la columna rut_ejecutivo ya existe
    $check_rut = $mysqli->query("SHOW COLUMNS FROM tb_experian_usuarios LIKE 'rut_ejecutivo'");
    
    if ($check_rut->num_rows === 0) {
        // La columna rut_ejecutivo no existe, agregarla
        $sql_rut = "ALTER TABLE tb_experian_usuarios ADD COLUMN rut_ejecutivo VARCHAR(12) AFTER proyecto";
        
        if (!$mysqli->query($sql_rut)) {
            throw new Exception("Error al agregar la columna rut_ejecutivo: " . $mysqli->error);
        }
        echo "✓ Columna 'rut_ejecutivo' agregada correctamente<br>\n";
    } else {
        echo "✓ La columna 'rut_ejecutivo' ya existe<br>\n";
    }
    
    // Actualizar registros existentes para establecer proyecto por defecto
    $update_proyecto = "UPDATE tb_experian_usuarios SET proyecto = 'inteletGroup' WHERE proyecto IS NULL OR proyecto = ''";
    
    if (!$mysqli->query($update_proyecto)) {
        throw new Exception("Error al actualizar los valores de proyecto: " . $mysqli->error);
    }
    echo "✓ Valores de 'proyecto' actualizados a 'inteletGroup'<br>\n";
    
    // Mostrar la estructura actualizada de la tabla
    echo "<h3>Estructura actualizada de la tabla:</h3>\n";
    $describe = $mysqli->query("DESCRIBE tb_experian_usuarios");
    
    if ($describe) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Por defecto</th><th>Extra</th></tr>\n";
        
        while ($row = $describe->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h3>✅ Actualización del esquema completada exitosamente</h3>\n";
    
} catch (Exception $e) {
    echo "<h3>❌ Error: " . htmlspecialchars($e->getMessage()) . "</h3>\n";
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}
?>
