/* Enhanced Login Page Styles */

/* CSS Variables for consistent theming */
:root {
  --primary-color: #667eea;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-color: #f093fb;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
  --dark-color: #1f2937;
  --light-color: #f8fafc;
  --border-radius: 12px;
  --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Login Wrapper */
.enhanced-login-wrapper {
  min-height: 100vh;
  background: var(--primary-gradient);
  position: relative;
  overflow: hidden;
}

.enhanced-login-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>') no-repeat center center;
  background-size: cover;
  opacity: 0.3;
}

/* Login Container */
.login-container {
  position: relative;
  z-index: 10;
  max-width: 420px;
  width: 100%;
  margin: 0 auto;
  padding: 20px;
}

/* Logo Section */
.logo-section {
  margin-bottom: 2rem;
}

.logo-container {
  margin-bottom: 1rem;
}

.login-logo {
  max-width: 80px;
  height: auto;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.brand-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brand-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  font-weight: 400;
  margin-bottom: 0;
}

/* Login Card */
.login-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-title {
  color: var(--dark-color);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.login-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0;
}

/* Enhanced Message System */
.message-container {
  margin-bottom: 1.5rem;
}

.enhanced-message {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
  transform: translateY(-20px);
  opacity: 0;
  transition: var(--transition);
}

.enhanced-message.show {
  transform: translateY(0);
  opacity: 1;
}

.enhanced-message.hide {
  transform: translateY(-20px);
  opacity: 0;
}

/* Message Types */
.success-message {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-left: 4px solid var(--success-color);
  color: #065f46;
}

.error-message {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border-left: 4px solid var(--error-color);
  color: #991b1b;
}

.info-message {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid var(--info-color);
  color: #1e40af;
}

.loading-message {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-left: 4px solid #6b7280;
  color: #374151;
}

/* Message Components */
.message-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.message-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.message-text {
  font-size: 0.875rem;
  line-height: 1.4;
}

.message-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  margin-left: 0.5rem;
  border-radius: 4px;
  transition: var(--transition);
}

.message-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Progress Bar */
.message-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  width: 100%;
}

.loading-progress {
  background: linear-gradient(90deg, transparent, rgba(107, 114, 128, 0.5), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes progress-bar {
  from { width: 100%; }
  to { width: 0%; }
}

/* Loading Spinner */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced Form Styles */
.enhanced-form {
  margin-bottom: 1.5rem;
}

.enhanced-form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Input Container */
.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.enhanced-input {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 2.5rem;
  border: 2px solid #e5e7eb;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  background: #f9fafb;
}

.enhanced-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-container.focused {
  transform: translateY(-1px);
}

.input-container.valid .enhanced-input {
  border-color: var(--success-color);
  background: #f0fdf4;
}

.input-container.invalid .enhanced-input {
  border-color: var(--error-color);
  background: #fef2f2;
}

/* Input Icons */
.input-icon {
  position: absolute;
  left: 0.75rem;
  color: #6b7280;
  font-size: 1rem;
  transition: var(--transition);
}

.input-container.focused .input-icon {
  color: var(--primary-color);
}

.input-container.valid .input-icon {
  color: var(--success-color);
}

.input-container.invalid .input-icon {
  color: var(--error-color);
}

/* Password Toggle */
.password-toggle {
  position: absolute;
  right: 2.5rem;
  cursor: pointer;
  color: #6b7280;
  font-size: 1rem;
  transition: var(--transition);
  padding: 0.25rem;
  border-radius: 4px;
}

.password-toggle:hover {
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.1);
}

/* Input Validation */
.input-validation {
  position: absolute;
  right: 0.75rem;
  font-size: 1rem;
}

.validation-success {
  color: var(--success-color);
  display: none;
}

.validation-error {
  color: var(--error-color);
  display: none;
}

.input-container.valid .validation-success {
  display: block;
}

.input-container.invalid .validation-error {
  display: block;
}

/* Field Feedback */
.field-feedback {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  transition: var(--transition);
}

.field-feedback.error {
  color: var(--error-color);
}

/* Enhanced Submit Button */
.enhanced-submit-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--border-radius);
  color: white;
  font-weight: 600;
  font-size: 1rem;
  padding: 0.875rem 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.enhanced-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.enhanced-submit-btn:active {
  transform: translateY(0);
}

.enhanced-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.enhanced-submit-btn.loading {
  background: #6b7280;
}

.enhanced-submit-btn.success {
  background: var(--success-color);
}

.btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Login Footer */
.login-footer {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.security-info {
  color: #6b7280;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    padding: 15px;
  }
  
  .login-card {
    padding: 1.5rem;
  }
  
  .brand-title {
    font-size: 1.75rem;
  }
  
  .enhanced-input {
    padding: 0.625rem 2.5rem 0.625rem 2rem;
  }
}

/* Animation Classes */
.animate__shakeX {
  animation: shakeX 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes shakeX {
  10%, 90% { transform: translate3d(-10px, 0, 0); }
  20%, 80% { transform: translate3d(20px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-40px, 0, 0); }
  40%, 60% { transform: translate3d(40px, 0, 0); }
}
