<?php
// Incluir el archivo de conexión
require_once 'con_db.php';
?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="GESTAR INTRANET">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <!-- Cache control headers -->
  <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>PORTAL GESTAR - LOGIN</title>

  <!-- Favicon -->
  <link rel="icon" href="img/icons/logoGestar.ico">
  <link rel="apple-touch-icon" href="img/icons/logoGestar.ico">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">

  <!-- jQuery -->
  <script src="js/jquery-3.7.1.min.js"></script>
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Login Wrapper Area -->
  <div class="login-wrapper d-flex align-items-center justify-content-center">
    <div class="custom-container">
      <div class="text-center px-4">
        <img class="login-intro-img" src="logoGestar.png" alt="">
      </div>

      <!-- Register Form -->
      <div class="register-form mt-4">
        <h6 class="mb-3 text-center">Ingresa con tus credenciales para continuar</h6>

        <!-- Mensaje de error -->
        <div id="error-message" class="alert alert-danger mb-3" style="display: none;"></div>
        
        <!-- Mensaje de información -->
        <div id="info-message" class="alert alert-info mb-3" style="display: none;"></div>

        <form id="login-form" method="POST">
          <div class="form-group">
            <input class="form-control" type="text" id="rut" name="rut" placeholder="RUT de usuario" required>
          </div>

          <div class="form-group position-relative">
            <input class="form-control" id="clave" name="clave" type="password" placeholder="Contraseña" required>
            <div class="position-absolute" id="password-visibility">
              <i class="bi bi-eye"></i>
              <i class="bi bi-eye-slash"></i>
            </div>
          </div>
          <button class="btn btn-primary w-100" type="submit">Ingresar</button>
        </form>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>

  <script>
    // Función para mostrar mensajes de error
    function mostrarError(mensaje) {
      $('#error-message').text(mensaje).show();
      setTimeout(function() {
        $('#error-message').fadeOut('slow');
      }, 5000);
    }

    // Función para mostrar mensajes informativos
    function mostrarInfo(mensaje) {
      $('#info-message').text(mensaje).show();
      setTimeout(function() {
        $('#info-message').fadeOut('slow');
      }, 5000);
    }

    // Función para registrar errores en consola
    function logError(error, info) {
      console.error('Error en login:', error);
      console.info('Info adicional:', info);
    }

    $(document).ready(function() {
      // Limpiar mensajes al cargar
      $('#error-message, #info-message').hide();

      // Limpiar la caché local para evitar problemas
      if ('caches' in window) {
        caches.keys().then(function(names) {
          names.forEach(function(name) {
            caches.delete(name);
          });
        });
      }

      // Prevenir resubmisión del formulario
      if (window.history.replaceState) {
        window.history.replaceState(null, null, window.location.href);
      }

      // Toggle password visibility
      $('#password-visibility').on('click', function() {
        const passwordInput = $('#clave');
        const type = passwordInput.attr('type') === 'password' ? 'text' : 'password';
        passwordInput.attr('type', type);
        $(this).find('.bi-eye, .bi-eye-slash').toggleClass('d-none');
      });

      // Manejar el envío del formulario
      $('#login-form').on('submit', function(e) {
        e.preventDefault();
        
        // Mostrar mensaje de espera
        mostrarInfo("Verificando credenciales...");
        
        // Deshabilitar el botón de submit para prevenir doble envío
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Procesando...');

        // Obtener valores del formulario
        const rut = $('#rut').val();
        const clave = $('#clave').val();
        
        // Verificar datos mínimos
        if (!rut || !clave) {
          mostrarError("Por favor complete todos los campos");
          submitBtn.prop('disabled', false);
          submitBtn.html('Ingresar');
          return;
        }

        // Crear FormData para enviar datos
        const formData = new FormData();
        formData.append('rut', rut);
        formData.append('clave', clave);

        // Configurar timeout para la petición
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 segundos timeout

        // Realizar la solicitud fetch en lugar de ajax
        fetch('ControllerGestar.php', {
          method: 'POST',
          body: formData,
          signal: controller.signal,
          cache: 'no-store'
        })
        .then(response => {
          clearTimeout(timeoutId);
          
          // Verificar si la respuesta es OK
          if (!response.ok) {
            throw new Error(`Error HTTP: ${response.status} ${response.statusText}`);
          }
          
          // Verificar el tipo de contenido
          const contentType = response.headers.get('content-type');
          if (!contentType || !contentType.includes('application/json')) {
            throw new Error(`Tipo de contenido inesperado: ${contentType}`);
          }
          
          return response.json();
        })
        .then(data => {
          console.log('Respuesta del servidor:', data);
          
          if (data.success) {
            // Login exitoso
            mostrarInfo(data.message || 'Login exitoso, redirigiendo...');
            
            // Retrasar la redirección para mostrar el mensaje
            setTimeout(function() {
              // Verificar si hay una URL de redirección en la respuesta
              if (data.redirectUrl) {
                window.location.href = data.redirectUrl;
              } else {
                // URL por defecto si no hay redirección específica
                window.location.href = 'form_experian.php';
              }
            }, 1000);
          } else {
            // Error en login
            mostrarError(data.message || 'Error en la autenticación');
            submitBtn.prop('disabled', false);
            submitBtn.html('Ingresar');
          }
        })
        .catch(error => {
          clearTimeout(timeoutId);
          
          // Manejar diferentes tipos de errores
          if (error.name === 'AbortError') {
            mostrarError('La solicitud ha excedido el tiempo de espera. Por favor inténtelo nuevamente.');
          } else if (error.message.includes('Failed to fetch')) {
            mostrarError('No se pudo conectar con el servidor. Verifique su conexión a internet.');
          } else {
            mostrarError('Error en la conexión: ' + error.message);
          }
          
          logError(error, { rut: rut });
          submitBtn.prop('disabled', false);
          submitBtn.html('Ingresar');
        });
      });

      // Mostrar mensaje de error si existe en la URL
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('error') === '1') {
        mostrarError('Credenciales incorrectas');
      }
    });
  </script>
</body>

</html>